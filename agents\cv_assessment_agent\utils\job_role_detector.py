"""
Job Role Detection Service

This module provides AI-based job role detection from CV content to enable
dynamic job-specific qualification questioning.
"""

import logging
import re
from typing import Dict, List, Optional, Any
from langchain_google_genai import ChatGoogleGenerativeAI

logger = logging.getLogger("job_role_detector")


class JobRoleDetector:
    """Detects job roles and professions from CV content using AI analysis."""

    def __init__(self, model: Optional[ChatGoogleGenerativeAI] = None):
        """Initialize the job role detector."""
        self.model = model
        
        # Define job role categories and their qualification requirements
        self.job_role_qualifications = {
            "driver": {
                "required_qualifications": ["drivers_license"],
                "optional_qualifications": ["safety_certifications"],
                "keywords": ["driver", "driving", "transport", "delivery", "truck", "taxi", "uber", "lyft", "chauffeur"]
            },
            "doctor": {
                "required_qualifications": ["medical_degree", "professional_license"],
                "optional_qualifications": ["industry_certifications"],
                "keywords": ["doctor", "physician", "md", "mbbs", "medical", "hospital", "clinic", "patient", "surgery"]
            },
            "engineer": {
                "required_qualifications": ["engineering_degree"],
                "optional_qualifications": ["industry_certifications", "professional_license"],
                "keywords": ["engineer", "engineering", "technical", "design", "development", "software", "mechanical", "electrical", "civil"]
            },
            "lawyer": {
                "required_qualifications": ["legal_qualification", "professional_license"],
                "optional_qualifications": ["industry_certifications"],
                "keywords": ["lawyer", "attorney", "legal", "law", "court", "litigation", "counsel", "advocate", "barrister"]
            },
            "teacher": {
                "required_qualifications": ["teaching_certification"],
                "optional_qualifications": ["industry_certifications"],
                "keywords": ["teacher", "educator", "professor", "instructor", "teaching", "education", "school", "university", "academic"]
            },
            "nurse": {
                "required_qualifications": ["professional_license", "industry_certifications"],
                "optional_qualifications": ["safety_certifications"],
                "keywords": ["nurse", "nursing", "rn", "lpn", "healthcare", "patient care", "medical assistant", "clinical"]
            },
            "pilot": {
                "required_qualifications": ["professional_license", "industry_certifications"],
                "optional_qualifications": ["safety_certifications"],
                "keywords": ["pilot", "aviation", "aircraft", "flight", "airline", "commercial pilot", "captain", "first officer"]
            },
            "accountant": {
                "required_qualifications": ["industry_certifications"],
                "optional_qualifications": ["professional_license"],
                "keywords": ["accountant", "accounting", "cpa", "finance", "financial", "bookkeeping", "audit", "tax"]
            },
            "security": {
                "required_qualifications": ["security_clearance", "professional_license"],
                "optional_qualifications": ["safety_certifications", "industry_certifications"],
                "keywords": ["security", "guard", "protection", "surveillance", "safety", "law enforcement", "police"]
            },
            "construction": {
                "required_qualifications": ["safety_certifications"],
                "optional_qualifications": ["professional_license", "industry_certifications"],
                "keywords": ["construction", "builder", "contractor", "carpenter", "electrician", "plumber", "mason", "foreman"]
            },
            "chef": {
                "required_qualifications": ["industry_certifications"],
                "optional_qualifications": ["safety_certifications"],
                "keywords": ["chef", "cook", "culinary", "kitchen", "restaurant", "food service", "catering", "baker"]
            },
            "pharmacist": {
                "required_qualifications": ["professional_license", "industry_certifications"],
                "optional_qualifications": [],
                "keywords": ["pharmacist", "pharmacy", "pharmaceutical", "medication", "drug", "prescription", "clinical pharmacy"]
            }
        }

    def detect_job_roles(self, cv_text: str) -> List[Dict[str, Any]]:
        """
        Detect job roles from CV content using both pattern matching and AI analysis.
        
        Args:
            cv_text: The CV text to analyze
            
        Returns:
            List of detected job roles with confidence scores and required qualifications
        """
        try:
            # First, try pattern-based detection for quick results
            pattern_roles = self._detect_roles_by_patterns(cv_text)
            
            # Then use AI analysis for more comprehensive detection
            ai_roles = self._detect_roles_by_ai(cv_text) if self.model else []
            
            # Combine and deduplicate results
            combined_roles = self._combine_role_detections(pattern_roles, ai_roles)
            
            # Add qualification requirements to each detected role
            for role in combined_roles:
                role_key = role["role"].lower()
                if role_key in self.job_role_qualifications:
                    role["required_qualifications"] = self.job_role_qualifications[role_key]["required_qualifications"]
                    role["optional_qualifications"] = self.job_role_qualifications[role_key]["optional_qualifications"]
                else:
                    role["required_qualifications"] = ["industry_certifications"]
                    role["optional_qualifications"] = ["professional_license"]
            
            return combined_roles
            
        except Exception as e:
            logger.error(f"Error detecting job roles: {str(e)}")
            return []

    def _detect_roles_by_patterns(self, cv_text: str) -> List[Dict[str, Any]]:
        """Detect job roles using keyword pattern matching."""
        cv_lower = cv_text.lower()
        detected_roles = []
        
        for role, config in self.job_role_qualifications.items():
            confidence = 0.0
            keyword_matches = 0
            
            # Count keyword matches
            for keyword in config["keywords"]:
                if keyword in cv_lower:
                    keyword_matches += 1
                    confidence += 0.1
            
            # Boost confidence for job titles and role-specific patterns
            title_patterns = [
                rf"\b{role}\b",
                rf"\b{role}s?\b",
                rf"senior\s+{role}",
                rf"junior\s+{role}",
                rf"lead\s+{role}",
                rf"{role}\s+(?:position|role|job)"
            ]
            
            for pattern in title_patterns:
                if re.search(pattern, cv_lower):
                    confidence += 0.3
                    break
            
            # Only include roles with reasonable confidence
            if confidence >= 0.2:
                detected_roles.append({
                    "role": role.title(),
                    "confidence": min(confidence, 1.0),
                    "detection_method": "pattern",
                    "keyword_matches": keyword_matches
                })
        
        # Sort by confidence
        detected_roles.sort(key=lambda x: x["confidence"], reverse=True)
        return detected_roles[:3]  # Return top 3 matches

    def _detect_roles_by_ai(self, cv_text: str) -> List[Dict[str, Any]]:
        """Detect job roles using AI analysis."""
        try:
            prompt = self._build_role_detection_prompt(cv_text)
            response = self.model.invoke(prompt)
            response_text = response.content if hasattr(response, "content") else str(response)
            
            # Parse AI response
            return self._parse_ai_role_response(response_text)
            
        except Exception as e:
            logger.error(f"Error in AI role detection: {str(e)}")
            return []

    def _build_role_detection_prompt(self, cv_text: str) -> str:
        """Build the AI prompt for job role detection."""
        available_roles = list(self.job_role_qualifications.keys())
        
        return f"""
Analyze the following CV and identify the primary job roles/professions of the candidate.

Available job role categories: {', '.join(available_roles)}

CV Text:
{cv_text}

Based on the candidate's experience, education, and skills, identify the most relevant job roles.
Return a JSON array with up to 3 job roles, each containing:
- role: the job role name (from the available categories)
- confidence: confidence score (0.0 to 1.0)
- reasoning: brief explanation for the detection

Example format:
[
  {{"role": "engineer", "confidence": 0.9, "reasoning": "Has engineering degree and software development experience"}},
  {{"role": "teacher", "confidence": 0.3, "reasoning": "Mentioned some training and mentoring activities"}}
]

Return only the JSON array, no additional text.
"""

    def _parse_ai_role_response(self, response_text: str) -> List[Dict[str, Any]]:
        """Parse AI response for job role detection."""
        try:
            import json
            
            # Extract JSON from response
            json_match = re.search(r'\[[\s\S]*\]', response_text)
            if json_match:
                roles_data = json.loads(json_match.group(0))
                
                # Validate and format the response
                formatted_roles = []
                for role_data in roles_data:
                    if isinstance(role_data, dict) and "role" in role_data:
                        formatted_roles.append({
                            "role": role_data["role"].title(),
                            "confidence": float(role_data.get("confidence", 0.5)),
                            "detection_method": "ai",
                            "reasoning": role_data.get("reasoning", "")
                        })
                
                return formatted_roles
                
        except Exception as e:
            logger.error(f"Error parsing AI role response: {str(e)}")
        
        return []

    def _combine_role_detections(self, pattern_roles: List[Dict[str, Any]], ai_roles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Combine pattern and AI detection results."""
        combined = {}
        
        # Add pattern-based detections
        for role in pattern_roles:
            role_key = role["role"].lower()
            combined[role_key] = role
        
        # Merge AI detections (boost confidence if both methods agree)
        for role in ai_roles:
            role_key = role["role"].lower()
            if role_key in combined:
                # Both methods detected this role - boost confidence
                combined[role_key]["confidence"] = min(
                    combined[role_key]["confidence"] + role["confidence"] * 0.5, 1.0
                )
                combined[role_key]["detection_method"] = "pattern+ai"
            else:
                combined[role_key] = role
        
        # Convert back to list and sort by confidence
        result = list(combined.values())
        result.sort(key=lambda x: x["confidence"], reverse=True)
        
        return result[:3]  # Return top 3 matches

    def get_qualification_requirements(self, job_role: str) -> Dict[str, List[str]]:
        """Get qualification requirements for a specific job role."""
        role_key = job_role.lower()
        if role_key in self.job_role_qualifications:
            return {
                "required_qualifications": self.job_role_qualifications[role_key]["required_qualifications"],
                "optional_qualifications": self.job_role_qualifications[role_key]["optional_qualifications"]
            }
        return {
            "required_qualifications": ["industry_certifications"],
            "optional_qualifications": ["professional_license"]
        }
