-- SQL Script to Add Job Qualifications Category to Existing Database
-- Run this in Supabase SQL Editor to add the new job_qualifications category

-- Add the job_qualifications category
INSERT INTO tag_categories (name, display_name, description, default_threshold, priority) VALUES
('job_qualifications', 'Job Qualifications', 'Job-specific qualifications, licenses, and certifications required for the role', 0.80, 7)
ON CONFLICT (name) DO NOTHING;

-- Add job qualification tag definitions
DO $$
DECLARE
    cat_id UUID;
BEGIN
    -- Get the job_qualifications category ID
    SELECT id INTO cat_id FROM tag_categories WHERE name = 'job_qualifications';
    
    -- Insert job qualification tag definitions
    INSERT INTO tag_definitions (category_id, tag_key, display_name, data_type, extraction_hints, question_templates, is_required, weight) VALUES
    (cat_id, 'professional_license', 'Professional License', 'text', 
     ARRAY['license', 'licensed', 'professional license', 'permit', 'certification'], 
     ARRAY['Do you have the required professional license for this role?', 'What professional licenses do you hold?'], true, 2.0),
    (cat_id, 'drivers_license', 'Driver''s License', 'text', 
     ARRAY['driver license', 'driving license', 'cdl', 'commercial license'], 
     ARRAY['Do you have a valid driver''s license?', 'What type of driver''s license do you hold?'], false, 1.5),
    (cat_id, 'medical_degree', 'Medical Degree', 'text', 
     ARRAY['md', 'medical degree', 'doctor', 'physician', 'mbbs'], 
     ARRAY['Do you have a medical degree (MD/MBBS)?', 'What medical qualifications do you have?'], false, 2.0),
    (cat_id, 'phd_doctorate', 'PhD/Doctorate', 'text', 
     ARRAY['phd', 'doctorate', 'doctoral degree', 'doctor of philosophy'], 
     ARRAY['Do you have a PhD or doctorate degree?', 'What is your doctoral specialization?'], false, 1.8),
    (cat_id, 'engineering_degree', 'Engineering Degree', 'text', 
     ARRAY['engineering degree', 'engineer', 'bachelor of engineering', 'be', 'btech'], 
     ARRAY['Do you have an engineering degree?', 'What type of engineering degree do you hold?'], false, 1.5),
    (cat_id, 'legal_qualification', 'Legal Qualification', 'text', 
     ARRAY['law degree', 'llb', 'jd', 'bar exam', 'attorney', 'lawyer'], 
     ARRAY['Do you have a law degree or legal qualification?', 'Are you admitted to the bar?'], false, 2.0),
    (cat_id, 'teaching_certification', 'Teaching Certification', 'text', 
     ARRAY['teaching certificate', 'education degree', 'teaching license', 'certified teacher'], 
     ARRAY['Do you have teaching certification?', 'What teaching qualifications do you hold?'], false, 1.5),
    (cat_id, 'safety_certifications', 'Safety Certifications', 'text', 
     ARRAY['safety certification', 'osha', 'safety training', 'first aid', 'cpr'], 
     ARRAY['Do you have required safety certifications?', 'What safety training have you completed?'], false, 1.3),
    (cat_id, 'industry_certifications', 'Industry Certifications', 'text', 
     ARRAY['certification', 'certified', 'professional certification', 'industry standard'], 
     ARRAY['Do you have relevant industry certifications?', 'What professional certifications do you hold?'], false, 1.5),
    (cat_id, 'security_clearance', 'Security Clearance', 'text', 
     ARRAY['security clearance', 'clearance', 'classified', 'background check'], 
     ARRAY['Do you have security clearance?', 'What level of security clearance do you hold?'], false, 1.8),
    (cat_id, 'nursing_license', 'Nursing License', 'text', 
     ARRAY['nursing license', 'rn', 'lpn', 'registered nurse', 'licensed nurse'], 
     ARRAY['Do you have a nursing license?', 'What type of nursing license do you hold?'], false, 2.0),
    (cat_id, 'pilot_license', 'Pilot License', 'text', 
     ARRAY['pilot license', 'commercial pilot', 'private pilot', 'atp', 'flight training'], 
     ARRAY['Do you have a pilot license?', 'What type of pilot license do you hold?'], false, 2.0),
    (cat_id, 'financial_certifications', 'Financial Certifications', 'text', 
     ARRAY['cpa', 'cfa', 'financial certification', 'accounting certification'], 
     ARRAY['Do you have financial certifications (CPA, CFA, etc.)?', 'What financial certifications do you hold?'], false, 1.8),
    (cat_id, 'trade_certifications', 'Trade Certifications', 'text', 
     ARRAY['trade certification', 'apprenticeship', 'journeyman', 'master craftsman'], 
     ARRAY['Do you have trade certifications?', 'What trade qualifications do you hold?'], false, 1.5),
    (cat_id, 'food_safety_certification', 'Food Safety Certification', 'text', 
     ARRAY['food safety', 'servsafe', 'haccp', 'food handler'], 
     ARRAY['Do you have food safety certifications?', 'What food safety training have you completed?'], false, 1.3)
    ON CONFLICT (category_id, tag_key) DO NOTHING;
END $$;

-- Verify the insertion
SELECT 
    tc.name as category_name,
    tc.display_name,
    COUNT(td.id) as tag_count
FROM tag_categories tc
LEFT JOIN tag_definitions td ON tc.id = td.category_id
WHERE tc.name = 'job_qualifications'
GROUP BY tc.id, tc.name, tc.display_name;
