-- SQL Script to Add Job Qualifications Category to Existing Database
-- Run this in Supabase SQL Editor to add the new job_qualifications category

-- Add the job_qualifications category
INSERT INTO tag_categories (name, display_name, description, default_threshold, priority) VALUES
('job_qualifications', 'Job Qualifications', 'Job-specific qualifications, licenses, and certifications required for the role', 0.80, 7)
ON CONFLICT (name) DO NOTHING;

-- Add job qualification tag definitions
DO $$
DECLARE
    cat_id UUID;
BEGIN
    -- Get the job_qualifications category ID
    SELECT id INTO cat_id FROM tag_categories WHERE name = 'job_qualifications';
    
    -- Insert job qualification tag definitions (more generic and useful)
    INSERT INTO tag_definitions (category_id, tag_key, display_name, data_type, extraction_hints, question_templates, is_required, weight) VALUES
    (cat_id, 'professional_certifications', 'Professional Certifications', 'text',
     ARRAY['certification', 'certified', 'aws', 'azure', 'gcp', 'oracle', 'microsoft', 'cisco', 'comptia'],
     ARRAY['Do you have any professional certifications relevant to your field?', 'What certifications do you hold?'], false, 2.0),
    (cat_id, 'licenses_permits', 'Licenses and Permits', 'text',
     ARRAY['license', 'licensed', 'permit', 'cdl', 'driver license', 'professional license'],
     ARRAY['Do you have any required licenses for your profession?', 'What licenses or permits do you hold?'], false, 1.8),
    (cat_id, 'technical_qualifications', 'Technical Qualifications', 'text',
     ARRAY['technical certification', 'programming certification', 'cloud certification', 'devops', 'security certification'],
     ARRAY['Do you have technical certifications relevant to your role?', 'What technical qualifications do you possess?'], false, 1.5),
    (cat_id, 'regulatory_compliance', 'Regulatory Compliance', 'text',
     ARRAY['compliance', 'regulatory', 'safety training', 'osha', 'hipaa', 'gdpr', 'sox'],
     ARRAY['Do you have training in regulatory compliance?', 'What compliance certifications do you have?'], false, 1.3),
    (cat_id, 'continuing_education', 'Continuing Education', 'text',
     ARRAY['continuing education', 'professional development', 'training', 'workshop', 'seminar'],
     ARRAY['Do you participate in continuing education for your field?', 'What recent training have you completed?'], false, 1.2)
    ON CONFLICT (category_id, tag_key) DO NOTHING;
END $$;

-- Verify the insertion
SELECT 
    tc.name as category_name,
    tc.display_name,
    COUNT(td.id) as tag_count
FROM tag_categories tc
LEFT JOIN tag_definitions td ON tc.id = td.category_id
WHERE tc.name = 'job_qualifications'
GROUP BY tc.id, tc.name, tc.display_name;
